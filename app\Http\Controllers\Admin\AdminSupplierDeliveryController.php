<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SupplierDelivery;
use App\Models\WarehouseStock;
use App\Models\StockMovement;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminSupplierDeliveryController extends Controller
{
    /**
     * Display a listing of supplier deliveries.
     */
    public function index(Request $request)
    {
        $query = SupplierDelivery::with(['supplier', 'product', 'receivedBy']);

        // Scope to current admin's warehouse only
        $currentUser = auth()->user();
        if ($currentUser->isAdmin() && $currentUser->warehouse_id) {
            $query->where('warehouse_id', $currentUser->warehouse_id);
        }

        // Month filter - show all data by default, filter only if month is specified
        $filterMonth = $request->get('month');
        $startDate = null;
        $endDate = null;

        if ($filterMonth && $filterMonth !== 'all') {
            // Parse the filter month
            $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
            $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

            // Apply date filter only when month is selected
            $query->whereBetween('delivery_date', [$startDate, $endDate]);
        }
        
        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('supplier', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }
        
        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // Sort by
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['delivery_date', 'received_date', 'quantity', 'status', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }
        
        $deliveries = $query->paginate(10);

        // Load returns data for each delivery to calculate returnable quantities
        $deliveries->getCollection()->transform(function ($delivery) {
            // Get all processed returns for this specific supplier delivery
            // Include approved, in_transit, completed, and processed returns with accepted action
            $processedReturns = \App\Models\ReturnModel::where('supplier_delivery_id', $delivery->id)
                ->where(function ($query) {
                    $query->whereIn('status', ['approved', 'in_transit', 'completed'])
                          ->orWhere(function ($subQuery) {
                              $subQuery->where('status', 'processed')
                                       ->where('processing_action', 'accepted');
                          });
                })
                ->sum('quantity');

            // Calculate max returnable quantity using simplified logic: full quantity minus approved returns
            $maxReturnable = max(0, $delivery->quantity - $processedReturns);

            $delivery->approved_returns_quantity = $processedReturns;
            $delivery->max_returnable_quantity = $maxReturnable;

            return $delivery;
        });

        // Get statistics - apply same filtering logic as main query including warehouse scoping
        $statsQuery = SupplierDelivery::query();

        // Apply warehouse scoping to stats as well
        if ($currentUser->isAdmin() && $currentUser->warehouse_id) {
            $statsQuery->where('warehouse_id', $currentUser->warehouse_id);
        }

        if ($filterMonth && $filterMonth !== 'all') {
            $statsQuery->whereBetween('delivery_date', [$startDate, $endDate]);
        }

        $stats = [
            'total' => (clone $statsQuery)->count(),
            'pending' => (clone $statsQuery)->where('status', 'pending')->count(),
            'received' => (clone $statsQuery)->where('status', 'received')->count(),
            'partial' => (clone $statsQuery)->where('status', 'partial')->count(),
        ];
        
        // Get available months for filter dropdown
        $availableMonths = $this->getAvailableMonths();

        return view('admin.supplier-deliveries.index', compact('deliveries', 'stats', 'filterMonth', 'availableMonths'));
    }
    
    /**
     * Display the specified delivery.
     */
    public function show(SupplierDelivery $delivery)
    {
        // Check warehouse access
        $currentUser = auth()->user();
        if ($currentUser->isAdmin() && $currentUser->warehouse_id && $delivery->warehouse_id !== $currentUser->warehouse_id) {
            abort(403, 'Anda tidak memiliki akses ke pengiriman ini.');
        }

        $delivery->load(['supplier', 'product', 'receivedBy']);

        // Calculate returnable quantity for this delivery using proper foreign key relationship
        // Get all processed returns for this specific supplier delivery
        // Include approved, in_transit, completed, and processed returns with accepted action
        $processedReturns = \App\Models\ReturnModel::where('supplier_delivery_id', $delivery->id)
            ->where(function ($query) {
                $query->whereIn('status', ['approved', 'in_transit', 'completed'])
                      ->orWhere(function ($subQuery) {
                          $subQuery->where('status', 'processed')
                                   ->where('processing_action', 'accepted');
                      });
            })
            ->sum('quantity');

        // Calculate max returnable quantity using simplified logic: full quantity minus approved returns
        $maxReturnable = max(0, $delivery->quantity - $processedReturns);

        $delivery->approved_returns_quantity = $processedReturns;
        $delivery->max_returnable_quantity = $maxReturnable;

        return view('admin.supplier-deliveries.show', compact('delivery'));
    }
    
    /**
     * Note: Manual receiving is no longer needed in the simplified system.
     * All supplier deliveries are auto-received when created.
     * This method is kept for backward compatibility but will redirect to deliveries list.
     */
    public function receive(Request $request, SupplierDelivery $delivery)
    {
        return redirect()->route('admin.supplier-deliveries.index')
            ->with('info', 'Pengiriman telah diterima secara otomatis. Jika ada masalah, silakan buat permintaan retur.');
    }
    
    /**
     * Cancel a supplier delivery.
     */
    public function cancel(Request $request, SupplierDelivery $delivery)
    {
        // Check warehouse access
        $currentUser = auth()->user();
        if ($currentUser->isAdmin() && $currentUser->warehouse_id && $delivery->warehouse_id !== $currentUser->warehouse_id) {
            abort(403, 'Anda tidak memiliki akses ke pengiriman ini.');
        }

        // Only allow cancelling if delivery is pending
        if ($delivery->status !== 'pending') {
            return redirect()->route('admin.supplier-deliveries.index')
                ->with('error', 'Pengiriman ini sudah diproses sebelumnya');
        }

        $validatedData = $request->validate([
            'notes' => 'nullable|string|max:1000',
        ], [
            'notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        DB::transaction(function () use ($delivery, $validatedData) {
            // Update delivery status to cancelled
            $delivery->update([
                'status' => 'cancelled',
                'notes' => $validatedData['notes'] ?? 'Pengiriman dibatalkan oleh admin gudang',
                'received_by' => auth()->id(),
            ]);

            // Record stock movement for cancellation tracking
            // Note: Since this system doesn't track supplier inventory,
            // the products are considered "returned" to supplier conceptually
            StockMovement::create([
                'product_id' => $delivery->product_id,
                'type' => 'out',
                'source' => 'supplier',
                'quantity' => -$delivery->quantity,
                'previous_stock' => 0, // No warehouse stock was added
                'new_stock' => 0,
                'reference_type' => 'SupplierDelivery',
                'reference_id' => $delivery->id,
                'warehouse_id' => $delivery->warehouse_id, // Track warehouse for stock movement
                'notes' => 'Pembatalan pengiriman dari supplier: ' . $delivery->supplier->name .
                          ($validatedData['notes'] ? ' - ' . $validatedData['notes'] : ''),
                'created_by' => auth()->id(),
            ]);
        });

        return redirect()->route('admin.supplier-deliveries.index')
            ->with('success', 'Pengiriman berhasil dibatalkan. Produk dianggap dikembalikan ke supplier.');
    }

    /**
     * Get available months for filter dropdown.
     */
    private function getAvailableMonths()
    {
        $months = [];

        // Add "All Time" option
        $months[] = [
            'value' => 'all',
            'label' => 'Semua Waktu'
        ];

        // Get months from existing deliveries for current warehouse only
        $query = SupplierDelivery::selectRaw('DATE_FORMAT(delivery_date, "%Y-%m") as month');

        // Apply warehouse scoping
        $currentUser = auth()->user();
        if ($currentUser->isAdmin() && $currentUser->warehouse_id) {
            $query->where('warehouse_id', $currentUser->warehouse_id);
        }

        $deliveryMonths = $query->distinct()
            ->orderBy('month', 'desc')
            ->pluck('month');

        foreach ($deliveryMonths as $month) {
            $date = Carbon::createFromFormat('Y-m', $month);
            $months[] = [
                'value' => $month,
                'label' => $date->format('F Y')
            ];
        }

        return $months;
    }
}
